create table `tb_log_202501` (
	`id` bigint not null auto_increment comment '主键',
	`platform` varchar(20) comment '平台',
	`version` varchar(10) comment '版本',
	`type` varchar(1) comment '用户类型',
	`user_id` bigint comment '用户编号',
	`ip` varchar(32) comment 'IP',
	`url` varchar(64) comment '地址',
	`request_id` varchar(64) comment '请求编号',
	`start_time` datetime comment '开始时间',
	`end_time` datetime comment '结束时间',
	`duration` int comment '执行时长',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户日志表';
alter table `tb_log_202501` add index `idx_log_01` (`user_id`);
alter table `tb_log_202501` add index `idx_log_02` (`url`);
alter table `tb_log_202501` add index `idx_log_03` (`start_time`);
create table `tb_log_202502` (
	`id` bigint not null auto_increment comment '主键',
	`platform` varchar(20) comment '平台',
	`version` varchar(10) comment '版本',
	`type` varchar(1) comment '用户类型',
	`user_id` bigint comment '用户编号',
	`ip` varchar(32) comment 'IP',
	`url` varchar(64) comment '地址',
	`request_id` varchar(64) comment '请求编号',
	`start_time` datetime comment '开始时间',
	`end_time` datetime comment '结束时间',
	`duration` int comment '执行时长',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户日志表';
alter table `tb_log_202502` add index `idx_log_01` (`user_id`);
alter table `tb_log_202502` add index `idx_log_02` (`url`);
alter table `tb_log_202502` add index `idx_log_03` (`start_time`);
create table `tb_log_202503` (
	`id` bigint not null auto_increment comment '主键',
	`platform` varchar(20) comment '平台',
	`version` varchar(10) comment '版本',
	`type` varchar(1) comment '用户类型',
	`user_id` bigint comment '用户编号',
	`ip` varchar(32) comment 'IP',
	`url` varchar(64) comment '地址',
	`request_id` varchar(64) comment '请求编号',
	`start_time` datetime comment '开始时间',
	`end_time` datetime comment '结束时间',
	`duration` int comment '执行时长',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户日志表';
alter table `tb_log_202503` add index `idx_log_01` (`user_id`);
alter table `tb_log_202503` add index `idx_log_02` (`url`);
alter table `tb_log_202503` add index `idx_log_03` (`start_time`);
create table `tb_log_202504` (
	`id` bigint not null auto_increment comment '主键',
	`platform` varchar(20) comment '平台',
	`version` varchar(10) comment '版本',
	`type` varchar(1) comment '用户类型',
	`user_id` bigint comment '用户编号',
	`ip` varchar(32) comment 'IP',
	`url` varchar(64) comment '地址',
	`request_id` varchar(64) comment '请求编号',
	`start_time` datetime comment '开始时间',
	`end_time` datetime comment '结束时间',
	`duration` int comment '执行时长',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户日志表';
alter table `tb_log_202504` add index `idx_log_01` (`user_id`);
alter table `tb_log_202504` add index `idx_log_02` (`url`);
alter table `tb_log_202504` add index `idx_log_03` (`start_time`);
create table `tb_log_202505` (
	`id` bigint not null auto_increment comment '主键',
	`platform` varchar(20) comment '平台',
	`version` varchar(10) comment '版本',
	`type` varchar(1) comment '用户类型',
	`user_id` bigint comment '用户编号',
	`ip` varchar(32) comment 'IP',
	`url` varchar(64) comment '地址',
	`request_id` varchar(64) comment '请求编号',
	`start_time` datetime comment '开始时间',
	`end_time` datetime comment '结束时间',
	`duration` int comment '执行时长',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户日志表';
alter table `tb_log_202505` add index `idx_log_01` (`user_id`);
alter table `tb_log_202505` add index `idx_log_02` (`url`);
alter table `tb_log_202505` add index `idx_log_03` (`start_time`);
create table `tb_log_202506` (
	`id` bigint not null auto_increment comment '主键',
	`platform` varchar(20) comment '平台',
	`version` varchar(10) comment '版本',
	`type` varchar(1) comment '用户类型',
	`user_id` bigint comment '用户编号',
	`ip` varchar(32) comment 'IP',
	`url` varchar(64) comment '地址',
	`request_id` varchar(64) comment '请求编号',
	`start_time` datetime comment '开始时间',
	`end_time` datetime comment '结束时间',
	`duration` int comment '执行时长',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户日志表';
alter table `tb_log_202506` add index `idx_log_01` (`user_id`);
alter table `tb_log_202506` add index `idx_log_02` (`url`);
alter table `tb_log_202506` add index `idx_log_03` (`start_time`);
create table `tb_log_202507` (
	`id` bigint not null auto_increment comment '主键',
	`platform` varchar(20) comment '平台',
	`version` varchar(10) comment '版本',
	`type` varchar(1) comment '用户类型',
	`user_id` bigint comment '用户编号',
	`ip` varchar(32) comment 'IP',
	`url` varchar(64) comment '地址',
	`request_id` varchar(64) comment '请求编号',
	`start_time` datetime comment '开始时间',
	`end_time` datetime comment '结束时间',
	`duration` int comment '执行时长',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户日志表';
alter table `tb_log_202507` add index `idx_log_01` (`user_id`);
alter table `tb_log_202507` add index `idx_log_02` (`url`);
alter table `tb_log_202507` add index `idx_log_03` (`start_time`);
create table `tb_log_202508` (
	`id` bigint not null auto_increment comment '主键',
	`platform` varchar(20) comment '平台',
	`version` varchar(10) comment '版本',
	`type` varchar(1) comment '用户类型',
	`user_id` bigint comment '用户编号',
	`ip` varchar(32) comment 'IP',
	`url` varchar(64) comment '地址',
	`request_id` varchar(64) comment '请求编号',
	`start_time` datetime comment '开始时间',
	`end_time` datetime comment '结束时间',
	`duration` int comment '执行时长',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户日志表';
alter table `tb_log_202508` add index `idx_log_01` (`user_id`);
alter table `tb_log_202508` add index `idx_log_02` (`url`);
alter table `tb_log_202508` add index `idx_log_03` (`start_time`);
create table `tb_log_202509` (
	`id` bigint not null auto_increment comment '主键',
	`platform` varchar(20) comment '平台',
	`version` varchar(10) comment '版本',
	`type` varchar(1) comment '用户类型',
	`user_id` bigint comment '用户编号',
	`ip` varchar(32) comment 'IP',
	`url` varchar(64) comment '地址',
	`request_id` varchar(64) comment '请求编号',
	`start_time` datetime comment '开始时间',
	`end_time` datetime comment '结束时间',
	`duration` int comment '执行时长',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户日志表';
alter table `tb_log_202509` add index `idx_log_01` (`user_id`);
alter table `tb_log_202509` add index `idx_log_02` (`url`);
alter table `tb_log_202509` add index `idx_log_03` (`start_time`);
create table `tb_log_202510` (
	`id` bigint not null auto_increment comment '主键',
	`platform` varchar(20) comment '平台',
	`version` varchar(10) comment '版本',
	`type` varchar(1) comment '用户类型',
	`user_id` bigint comment '用户编号',
	`ip` varchar(32) comment 'IP',
	`url` varchar(64) comment '地址',
	`request_id` varchar(64) comment '请求编号',
	`start_time` datetime comment '开始时间',
	`end_time` datetime comment '结束时间',
	`duration` int comment '执行时长',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户日志表';
alter table `tb_log_202510` add index `idx_log_01` (`user_id`);
alter table `tb_log_202510` add index `idx_log_02` (`url`);
alter table `tb_log_202510` add index `idx_log_03` (`start_time`);
create table `tb_log_202511` (
	`id` bigint not null auto_increment comment '主键',
	`platform` varchar(20) comment '平台',
	`version` varchar(10) comment '版本',
	`type` varchar(1) comment '用户类型',
	`user_id` bigint comment '用户编号',
	`ip` varchar(32) comment 'IP',
	`url` varchar(64) comment '地址',
	`request_id` varchar(64) comment '请求编号',
	`start_time` datetime comment '开始时间',
	`end_time` datetime comment '结束时间',
	`duration` int comment '执行时长',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户日志表';
alter table `tb_log_202511` add index `idx_log_01` (`user_id`);
alter table `tb_log_202511` add index `idx_log_02` (`url`);
alter table `tb_log_202511` add index `idx_log_03` (`start_time`);
create table `tb_log_202512` (
	`id` bigint not null auto_increment comment '主键',
	`platform` varchar(20) comment '平台',
	`version` varchar(10) comment '版本',
	`type` varchar(1) comment '用户类型',
	`user_id` bigint comment '用户编号',
	`ip` varchar(32) comment 'IP',
	`url` varchar(64) comment '地址',
	`request_id` varchar(64) comment '请求编号',
	`start_time` datetime comment '开始时间',
	`end_time` datetime comment '结束时间',
	`duration` int comment '执行时长',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null comment '修改时间',
	`create_time` datetime not null comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '系统用户日志表';
alter table `tb_log_202512` add index `idx_log_01` (`user_id`);
alter table `tb_log_202512` add index `idx_log_02` (`url`);
alter table `tb_log_202512` add index `idx_log_03` (`start_time`);