package com.example.yin.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.example.yin.constant.Constants;
import com.example.yin.utils.FileUtil;

/**
 * 集中一下图像的配置类吧
 **/
@Configuration
public class WebPicConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/img/avatorImages/**")
                .addResourceLocations(FileUtil.adaptPath(Constants.AVATOR_IMAGES_PATH));
        registry.addResourceHandler("/img/singerPic/**")
                .addResourceLocations(FileUtil.adaptPath(Constants.SINGER_PIC_PATH));
        registry.addResourceHandler("/img/songPic/**")
                .addResourceLocations(FileUtil.adaptPath(Constants.SONG_PIC_PATH));
        registry.addResourceHandler("/song/**")
                .addResourceLocations(FileUtil.adaptPath(Constants.SONG_PATH));
        registry.addResourceHandler("/img/songListPic/**")
                .addResourceLocations(FileUtil.adaptPath(Constants.SONGLIST_PIC_PATH));
        registry.addResourceHandler("/img/swiper/**")
                .addResourceLocations(FileUtil.adaptPath(Constants.BANNER_PIC_PATH));
        registry.addResourceHandler("/files/**")
                .addResourceLocations(FileUtil.adaptPath(Constants.FILE_PATH));
    }

}
