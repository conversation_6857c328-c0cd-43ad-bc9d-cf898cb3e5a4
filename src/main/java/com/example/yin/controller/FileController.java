package com.example.yin.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.io.PrintWriter;

import javax.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.example.yin.common.R;
import com.example.yin.utils.FileUtil;

@RestController
public class FileController {

	/**
	 * 上传文件
	 * @param file
	 * @return
	 */
    @RequestMapping("/v1/file/upload")
    public R upload(MultipartFile file){
        try {
            String originalFilename = file.getOriginalFilename();
            // 生成目标文件
            File destFile = FileUtil.getDestFile(originalFilename);
            // 保存到磁盘
            file.transferTo(destFile);
            // 返回文件路径
            String path = destFile.getAbsolutePath().replace(FileUtil.getProjectPath(), "");
            return R.success("上传成功",path);
        }catch (Exception e){
            return R.error("上传失败");
        }
    }

    @RequestMapping("/v1/file/download")
    public void download(@RequestParam("url") String url,HttpServletResponse response){
			File destFile = new File(FileUtil.adaptPath(FileUtil.getProjectFilePath() + url));
	    	FileInputStream fis = null;
	    	OutputStream os = null;
	    	try{
	    		fis = new FileInputStream(destFile);
	    		byte[] data = new byte[fis.available()];
	    		fis.read(data);
	    		fis.close();
	    		os = response.getOutputStream();
	    		os.write(data);
	    		os.flush();
	    		os.close();
	    	}catch (Exception e) {
				try {
					PrintWriter writer = response.getWriter();
					writer.write(e.getMessage());
					writer.flush();
					writer.close();
				}catch (Exception ex){

				}
		}finally{
			if(os != null){
				try{
					os.close();
				}catch (Exception e) {

				}
			}
			if(fis != null){
				try{
					fis.close();
				}catch (Exception e) {

				}
			}
		}
    }




	@RequestMapping("/v1/file/download")
	public void download(@RequestParam("url") String url,HttpServletResponse response){
		File destFile = new File(FileUtil.adaptPath(FileUtil.getProjectFilePath() + url));
		FileInputStream fis = null;
		OutputStream os = null;
		try{
			fis = new FileInputStream(destFile);
			byte[] data = new byte[fis.available()];
			fis.read(data);
			fis.close();
			os = response.getOutputStream();
			os.write(data);
			os.flush();
			os.close();
		}catch (Exception e) {
			try {
				PrintWriter writer = response.getWriter();
				writer.write(e.getMessage());
				writer.flush();
				writer.close();
			}catch (Exception ex){

			}
		}finally{
			if(os != null){
				try{
					os.close();
				}catch (Exception e) {

				}
			}
			if(fis != null){
				try{
					fis.close();
				}catch (Exception e) {

				}
			}
		}
	}
}
