package com.example.yin.controller;

import java.io.File;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.example.yin.common.R;
import com.example.yin.utils.FileUtil;

@RestController
public class FileController {

    @RequestMapping("/v1/file/upload")
    public R upload(MultipartFile file){
        try {
            String originalFilename = file.getOriginalFilename();
            // 生成目标文件
            File destFile = FileUtil.getDestFile(originalFilename);
            // 保存到磁盘
            file.transferTo(destFile);
            // 返回文件路径
            String path = destFile.getAbsolutePath().replace(FileUtil.getProjectPath(), "");
            return R.success("上传成功",path);
        }catch (Exception e){
            return R.error("上传失败");
        }

    }
}
