package com.example.yin.utils;


import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Scanner;
import java.util.Set;

import com.example.yin.sequence.CommonSequence;

public class FileUtil {

    // 系统文件分隔符
    public static final String FILE_SEPARATOR = File.separator;

    /**
     * 将路径转换为当前系统的标准格式
     * @param path 原始路径
     * @return 适配当前系统的路径
     */
    public static String adaptPath(String path) {
        if (path == null || path.isEmpty()) {
            return path;
        }

        // 将各种可能的分隔符统一替换为当前系统的标准分隔符
        return path.replace("/", FILE_SEPARATOR)
                .replace("\\", FILE_SEPARATOR);
    }

    /**
     * 构建跨平台路径
     * @param parts 路径组成部分
     * @return 完整的标准路径
     */
    public static String buildPath(String... parts) {
        if (parts == null || parts.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < parts.length; i++) {
            sb.append(parts[i]);
            if (i < parts.length - 1) {
                sb.append(FILE_SEPARATOR);
            }
        }

        return sb.toString();
    }

    /**
     * 获取系统路径分隔符（用于多个路径的分隔）
     */
    public static String getPathSeparator() {
        return File.pathSeparator;
    }

    /**
     * 获取当前项目的路径
     * @return
     */
    public static String getProjectPath() {
        return System.getProperty("user.dir");
    }

    /**
     * 获取当前项目的文件路径
     * @return
     */
    public static String getProjectFilePath() {
        return System.getProperty("user.dir") + FILE_SEPARATOR + "files";
    }

    /**
     * 新建目录
     * @param dir
     */
    public static void make(String dir){
        File file = new File(dir);
        if(!file.exists()){
            file.mkdirs();
        }
    }
    /**
     * 读取目录下文件
     * @param dir 目录
     * @param isRecursive 是否递归读取
     * @param uriSet 返回文件路径集合
     */
    public static Set<String> list(String dir, boolean isRecursive, Set<String> uriSet) {
        File file = new File(dir);
        if(file.exists()){
            if(!file.isDirectory()){
                uriSet.add(file.getPath());
            }else if(file.isDirectory()) {
                File[] fileList = file.listFiles();
                if(fileList != null){
                    for(int i = 0; i < fileList.length; i++){
                        file = fileList[i];
                        if(!file.isDirectory()){
                            uriSet.add(file.getPath());
                        }else if(file.isDirectory()){
                            if(isRecursive){
                                list(fileList[i].getPath(),isRecursive,uriSet);
                            }
                        }
                    }
                }
            }
        }
        return uriSet;
    }
    /**
     * 目录或文件是否存在
     * @param path
     * @return
     */
    public static boolean exists(String path){
        File file = new File(path);
        return file.exists();
    }
    /**
     * 拷贝文件
     * @param src 源文件
     * @param dest 目标文件
     */
    public static void copy(String src,String dest){
        FileChannel in = null;
        FileChannel out = null;
        FileInputStream inStream = null;
        FileOutputStream outStream = null;
        try{
            inStream = new FileInputStream(src);
            outStream = new FileOutputStream(dest);
            in = inStream.getChannel();
            out = outStream.getChannel();
            in.transferTo(0, in.size(), out);
        }catch(Exception e) {
            throw new RuntimeException(e);
        }finally{
            if(inStream != null){
                try{
                    inStream.close();
                }catch(Exception e){
                    throw new RuntimeException(e);
                }
            }
            if(in != null){
                try{
                    in.close();
                }catch(Exception e){
                    throw new RuntimeException(e);
                }
            }
            if(outStream != null){
                try{
                    outStream.close();
                }catch(Exception e){
                    throw new RuntimeException(e);
                }
            }
            if(out != null){
                try{
                    out.close();
                }catch(Exception e){
                    throw new RuntimeException(e);
                }
            }
        }
    }
    /**
     * 读取文件内容
     * @param file
     * @return
     */
    public static String read(File file){
        Scanner scanner = null;
        StringBuffer buffer = new StringBuffer();
        try {
            scanner = new Scanner(file, StandardCharsets.UTF_8.name());
            while (scanner.hasNextLine()) {
                buffer.append(scanner.nextLine());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (scanner != null) {
                scanner.close();
            }
        }
        return buffer.toString();
    }

    public static File getDestFile(String fileName){
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String datetime = sdf.format(calendar.getTime());
        String dir = new StringBuilder(getProjectFilePath())
                .append(FILE_SEPARATOR).append(datetime.substring(0,4))
                .append(FILE_SEPARATOR).append(datetime.substring(4,6))
                .append(FILE_SEPARATOR).append(datetime.substring(6,8))
                .append(FILE_SEPARATOR).append(datetime.substring(8,10))
                .append(FILE_SEPARATOR).append(datetime.substring(10,12)).toString();
        File fileDir = new File(dir);
        if(!fileDir.exists()){
            fileDir.mkdirs();
        }
        File file = new File(dir,new StringBuilder()
                .append(datetime.substring(12,14))
                .append(CommonSequence.nextValue())
                .append(getExtension(fileName)).toString());
        return file;
    }

    public static String getExtension(String fileName){
        fileName = getName(fileName);
        int index = fileName.lastIndexOf(".");
        if(index == -1){
            return "";
        }
        return fileName.substring(index);
    }

    public static String getName(String fileName){
        int index = fileName.lastIndexOf(FILE_SEPARATOR);
        if(index == -1){
            return fileName;
        }
        return fileName.substring(index + 1);
    }

}

