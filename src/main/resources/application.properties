mybatis.typeAliasesPackage=com.example.yin.model.domain
mybatis.mapperLocations=classpath:mapper/*.xml
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
server.port=8888

spring.datasource.url=jdbc:mysql://*************:3306/music_server?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC
spring.datasource.username=crm
spring.datasource.password=crm
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

minio.endpoint=http://localhost:9000
minio.access-key=root
minio.secret-key=123456789
minio.bucket-name=user01
# æä»¶ä¸ä¼ çå¤§å°
spring.servlet.multipart.max-file-size= 300MB
# æä»¶ä¸ä¼ çæå¤§è¯·æ±å¤§å°
spring.servlet.multipart.max-request-size= 300MB

spring.devtools.restart.enabled=false

spring.devtools.restart.additional-paths=src/main/java

spring.devtools.restart.exclude=WEB-INF/**

logging.level.org.springframework.boot.autoconfigure=ERROR

spring.mail.host=smtp.163.com
spring.mail.port=465
spring.mail.username=
spring.mail.password=
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.starttls.enable=true
mail.address=
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
