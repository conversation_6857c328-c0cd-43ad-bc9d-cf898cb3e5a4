<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yin.mapper.SongMapper">
  <resultMap id="BaseResultMap" type="com.example.yin.model.domain.Song">
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="singer_id" property="singerId" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="introduction" property="introduction" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="pic" property="pic" jdbcType="VARCHAR" />
    <result column="url" property="url" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.example.yin.model.domain.Song" extends="BaseResultMap">
    <result column="lyric" property="lyric" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List">
    id, singer_id, name, introduction, create_time, update_time, pic
  </sql>
  <sql id="Blob_Column_List">
    lyric
  </sql>

</mapper>
