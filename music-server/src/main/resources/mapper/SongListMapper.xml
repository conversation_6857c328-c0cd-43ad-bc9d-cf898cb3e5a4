<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yin.mapper.SongListMapper">
  <resultMap id="BaseResultMap" type="com.example.yin.model.domain.SongList">
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="pic" property="pic" jdbcType="VARCHAR" />
    <result column="style" property="style" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.example.yin.model.domain.SongList" extends="BaseResultMap">
    <result column="introduction" property="introduction" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List">
    id, title, pic, style
  </sql>
  <sql id="Blob_Column_List">
    introduction
  </sql>

</mapper>
