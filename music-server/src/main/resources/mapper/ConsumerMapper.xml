<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yin.mapper.ConsumerMapper">
  <resultMap id="BaseResultMap" type="com.example.yin.model.domain.Consumer">
    <result column="id" property="id" jdbcType="INTEGER" />
    <result column="username" property="username" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="sex" property="sex" jdbcType="TINYINT" />
    <result column="phone_num" property="phoneNum" jdbcType="CHAR" />
    <result column="email" property="email" jdbcType="CHAR" />
    <result column="birth" property="birth" jdbcType="TIMESTAMP" />
    <result column="introduction" property="introduction" jdbcType="VARCHAR" />
    <result column="location" property="location" jdbcType="VARCHAR" />
    <result column="avator" property="avator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List">
    id, username, password, sex, phone_num, email, birth, introduction, location, avator, create_time, update_time
  </sql>

</mapper>
