<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yin.mapper.UserSupportMapper">

    <resultMap id="BaseResultMap" type="com.example.yin.model.domain.UserSupport">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="commentId" column="comment_id" jdbcType="INTEGER"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,comment_id,user_id
    </sql>
</mapper>
