<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yin.mapper.RankListMapper">
  <resultMap id="BaseResultMap" type="com.example.yin.model.domain.RankList">
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="songListId" property="songListId" jdbcType="BIGINT" />
    <result column="consumerId" property="consumerId" jdbcType="BIGINT" />
    <result column="score" property="score" jdbcType="INTEGER" />
  </resultMap>

  <select id="selectScoreSum" resultType="java.lang.Integer">
  SELECT COALESCE(sum(score), 0) as score from rank_list where song_list_id = #{0} ;
  </select>

  <select id="selectUserRank" resultType="java.lang.Integer" parameterType="com.example.yin.model.domain.RankList">
    select score
    from rank_list
    where consumer_id = #{consumer_id, jdbcType=BIGINT} and song_list_id = #{song_list_id, jdbcType=BIGINT}
  </select>

</mapper>