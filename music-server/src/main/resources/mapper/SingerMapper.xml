<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yin.mapper.SingerMapper">
  <resultMap id="BaseResultMap" type="com.example.yin.model.domain.Singer">
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="sex" property="sex" jdbcType="TINYINT" />
    <result column="pic" property="pic" jdbcType="VARCHAR" />
    <result column="birth" property="birth" jdbcType="TIMESTAMP" />
    <result column="location" property="location" jdbcType="VARCHAR" />
    <result column="introduction" property="introduction" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, sex, pic, birth, location, introduction
  </sql>

</mapper>
