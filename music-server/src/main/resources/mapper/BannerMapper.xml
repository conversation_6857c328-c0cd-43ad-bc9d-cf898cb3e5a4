<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yin.mapper.BannerMapper">

    <resultMap id="BaseResultMap" type="com.example.yin.model.domain.Banner">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="pic" column="pic" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,pic
    </sql>
</mapper>
