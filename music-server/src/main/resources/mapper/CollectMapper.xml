<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.yin.mapper.CollectMapper">
  <resultMap id="BaseResultMap" type="com.example.yin.model.domain.Collect">
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="user_id" property="userId" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="song_id" property="songId" jdbcType="INTEGER" />
    <result column="song_list_id" property="songListId" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, type, song_id, song_list_id, create_time
  </sql>

</mapper>
